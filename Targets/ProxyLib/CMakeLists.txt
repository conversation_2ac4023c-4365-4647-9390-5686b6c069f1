#
#  Copyright © 2025-Present, Arkin Terli. All rights reserved.
#
#  NOTICE:  All information contained herein is, and remains the property of Arkin Terli.
#  The intellectual and technical concepts contained herein are proprietary to Arkin Terli
#  and may be covered by U.S. and Foreign Patents, patents in process, and are protected by
#  trade secret or copyright law. Dissemination of this information or reproduction of this
#  material is strictly forbidden unless prior written permission is obtained from Arkin Terli.

set(TARGET_NAME ProxyLib)

set(LIB_SOURCES
        ProxyClient.cpp
)

add_library(${TARGET_NAME} STATIC ${LIB_SOURCES})

add_dependencies(${TARGET_NAME}
        zlib_cpp
        openssl_cpp
        httplib_cpp
        ixwebsocket_cpp
        spdlog_cpp
        json_cpp
)

if (APPLE)
    target_link_libraries(${TARGET_NAME} PRIVATE
            z
            ssl
            crypto
            ixwebsocket
            spdlog
    )
endif()
