#
#  Copyright © 2025-Present, <PERSON><PERSON>rl<PERSON>. All rights reserved.
#
#  NOTICE:  All information contained herein is, and remains the property of Arkin Terli.
#  The intellectual and technical concepts contained herein are proprietary to Arkin Terli
#  and may be covered by U.S. and Foreign Patents, patents in process, and are protected by
#  trade secret or copyright law. Dissemination of this information or reproduction of this
#  material is strictly forbidden unless prior written permission is obtained from Arkin Terli.

include(ExternalProject)

# ---------------------------------------------------------------------------------
# HELPER FUNCTIONS
# ---------------------------------------------------------------------------------

# Builds and installs external git projects.
function(add_external_git_project)
    set(options)
    set(oneValueArgs NAME URL GIT_REPOSITORY GIT_TAG GIT_SHALLOW SOURCE_SUBDIR EXTERNALS_BIN_DIR BUILD_TYPE)
    set(multiValueArgs CMAKE_ARGS)
    cmake_parse_arguments(ARG "${options}" "${oneValueArgs}" "${multiValueArgs}" ${ARGN})

    message(STATUS "Configuring External Project: ${ARG_NAME}")
    set(lib_dir "${ARG_EXTERNALS_BIN_DIR}/${ARG_NAME}")

    # By default, GIT_SHALLOW is ON.
    if ("${ARG_GIT_SHALLOW}" STREQUAL "")
        set(ARG_GIT_SHALLOW ON)
    endif()

    ExternalProject_Add(
            ${ARG_NAME}
            URL             ${ARG_URL}
            GIT_REPOSITORY  ${ARG_GIT_REPOSITORY}
            GIT_TAG         ${ARG_GIT_TAG}
            GIT_SHALLOW     ${ARG_GIT_SHALLOW}
            PREFIX          "${lib_dir}/prefix"
            SOURCE_DIR      "${lib_dir}/src"
            STAMP_DIR       "${lib_dir}/stamp"
            BINARY_DIR      "${lib_dir}/build"
            INSTALL_DIR     "${lib_dir}/install"
            DOWNLOAD_DIR    "${lib_dir}/download"
            LOG_DIR         "${lib_dir}/log"
            CMAKE_ARGS      -DCMAKE_BUILD_TYPE=${ARG_BUILD_TYPE}
            -DCMAKE_INSTALL_PREFIX=<INSTALL_DIR>
            ${ARG_CMAKE_ARGS}
            SOURCE_SUBDIR   ${ARG_SOURCE_SUBDIR}
            LOG_DOWNLOAD ON
            LOG_CONFIGURE ON
            LOG_BUILD ON
            LOG_INSTALL ON
            LOG_UPDATE ON
            LOG_PATCH ON
            LOG_TEST ON
            LOG_MERGED_STDOUTERR ON
            LOG_OUTPUT_ON_FAILURE ON
            GIT_SUBMODULES_RECURSE ON
            GIT_PROGRESS OFF
            BUILD_ALWAYS OFF
            UPDATE_COMMAND ""
    )

    # Make include and lib folders available to prevent linker warnings.
    file(MAKE_DIRECTORY "${lib_dir}/install/include" "${lib_dir}/install/lib")

    include_directories(${lib_dir}/install/include)
    link_directories(${lib_dir}/install/lib)
endfunction()

# Builds and installs external git projects with make.
function(add_external_git_project_with_make)
    set(options)
    set(oneValueArgs NAME URL URL_HASH URL_MD5 GIT_REPOSITORY GIT_TAG GIT_SHALLOW EXTERNALS_BIN_DIR)
    set(multiValueArgs CONFIG_ARGS TOUCH_FILES)
    cmake_parse_arguments(ARG "${options}" "${oneValueArgs}" "${multiValueArgs}" ${ARGN})

    message(STATUS "Configuring External Project: ${ARG_NAME}")
    set(lib_dir "${ARG_EXTERNALS_BIN_DIR}/${ARG_NAME}")

    # By default, GIT_SHALLOW is ON.
    if ("${ARG_GIT_SHALLOW}" STREQUAL "")
        set(ARG_GIT_SHALLOW ON)
    endif()

    # Find make command.
    find_program(make_executable NAMES make gmake nmake)
    if(NOT make_executable)
        message(FATAL_ERROR "Could not find make executable.")
    endif()

    # Create absolute paths for touch files. Touch updates timestamp of files to prevent build failure.
    set(absolute_touch_files "")
    foreach(file ${ARG_TOUCH_FILES})
        list(APPEND absolute_touch_files "${lib_dir}/src/${file}")
    endforeach()

    ExternalProject_Add(
            ${ARG_NAME}
            URL             ${ARG_URL}
            URL_HASH        ${ARG_URL_HASH}
            URL_MD5         ${ARG_URL_MD5}
            GIT_REPOSITORY  ${ARG_GIT_REPOSITORY}
            GIT_TAG         ${ARG_GIT_TAG}
            GIT_SHALLOW     ${ARG_GIT_SHALLOW}
            PREFIX          "${lib_dir}/prefix"
            SOURCE_DIR      "${lib_dir}/src"
            STAMP_DIR       "${lib_dir}/stamp"
            BINARY_DIR      "${lib_dir}/build"
            INSTALL_DIR     "${lib_dir}/install"
            DOWNLOAD_DIR    "${lib_dir}/download"
            LOG_DIR         "${lib_dir}/log"
            LOG_DOWNLOAD ON
            LOG_CONFIGURE ON
            LOG_BUILD ON
            LOG_INSTALL ON
            LOG_UPDATE ON
            LOG_PATCH ON
            LOG_TEST ON
            LOG_MERGED_STDOUTERR ON
            LOG_OUTPUT_ON_FAILURE ON
            GIT_SUBMODULES_RECURSE ON
            GIT_PROGRESS OFF
            BUILD_ALWAYS OFF
            CONFIGURE_COMMAND
            COMMAND ${lib_dir}/src/Configure ${ARG_CONFIG_ARGS} --prefix=${lib_dir}/install
            BUILD_COMMAND     ${make_executable}
            INSTALL_COMMAND   ${make_executable} install_sw
            UPDATE_COMMAND    ""
    )

    # Make include and lib folders available to prevent linker warnings.
    file(MAKE_DIRECTORY "${lib_dir}/install/include" "${lib_dir}/install/lib")

    include_directories(${lib_dir}/install/include)
    link_directories(${lib_dir}/install/lib)
endfunction()

# Download an external file.
function(download_external_file)
    set(options)
    set(oneValueArgs NAME URL DOWNLOAD_NAME DISABLE_EXTRACT EXTERNALS_BIN_DIR)
    set(multiValueArgs)
    cmake_parse_arguments(ARG "${options}" "${oneValueArgs}" "${multiValueArgs}" ${ARGN})

    message(STATUS "Configuring External File: ${ARG_NAME}")
    set(file_dir "${ARG_EXTERNALS_BIN_DIR}/${ARG_NAME}")

    if ("${ARG_DISABLE_EXTRACT}" STREQUAL "")
        set(ARG_DISABLE_EXTRACT ON)
    endif()

    ExternalProject_Add(
            ${ARG_NAME}
            URL             ${ARG_URL}
            DOWNLOAD_NAME   ${ARG_DOWNLOAD_NAME}
            PREFIX          "${file_dir}/prefix"
            SOURCE_DIR      "${file_dir}/src"
            STAMP_DIR       "${file_dir}/stamp"
            DOWNLOAD_DIR    "${file_dir}/download"
            LOG_DIR         "${file_dir}/log"
            CONFIGURE_COMMAND ""
            BUILD_COMMAND     ""
            INSTALL_COMMAND   ""
            UPDATE_COMMAND    ""
            LOG_DOWNLOAD ON
            DOWNLOAD_NO_EXTRACT ${ARG_DISABLE_EXTRACT}
    )
endfunction()

# ---------------------------------------------------------------------------------
# COMMON SETTINGS
# ---------------------------------------------------------------------------------

# Externals build and install folder.
set(EXTERNALS_BINARY_DIR "${CMAKE_BINARY_DIR}/Externals")

# Common cmake project settings for the external projects.
set(EXTERNAL_COMMON_CMAKE_ARGS
        -DCMAKE_C_COMPILER=${CMAKE_C_COMPILER}
        -DCMAKE_CXX_COMPILER=${CMAKE_CXX_COMPILER}
        -DCMAKE_MAKE_PROGRAM=${CMAKE_MAKE_PROGRAM}
        -DCMAKE_POSITION_INDEPENDENT_CODE=${CMAKE_POSITION_INDEPENDENT_CODE}
        -DCMAKE_BUILD_WITH_INSTALL_RPATH=${CMAKE_BUILD_WITH_INSTALL_RPATH}
        -DCMAKE_INSTALL_RPATH=${CMAKE_INSTALL_RPATH}
        -Wno-dev
)

if(MSVC)
    if("${CMAKE_MSVC_RUNTIME_LIBRARY}" STREQUAL "MultiThreaded")
        set(EXTERNALS_MSVC_RUNTIME_LIBRARY /MT)
        set(ANTLR4_WITH_STATIC_CRT ON)
    elseif("${CMAKE_MSVC_RUNTIME_LIBRARY}" STREQUAL "MultiThreadedDLL")
        set(EXTERNALS_MSVC_RUNTIME_LIBRARY /MD)
    else()
        message(FATAL_ERROR "CMAKE_MSVC_RUNTIME_LIBRARY value: '${CMAKE_MSVC_RUNTIME_LIBRARY}' is not supported.")
    endif()

    list(APPEND EXTERNAL_COMMON_CMAKE_ARGS
            -DCMAKE_C_FLAGS_DEBUG=${EXTERNALS_MSVC_RUNTIME_LIBRARY}
            -DCMAKE_C_FLAGS_RELEASE=${EXTERNALS_MSVC_RUNTIME_LIBRARY}
            -DCMAKE_CXX_FLAGS_DEBUG=${EXTERNALS_MSVC_RUNTIME_LIBRARY}
            -DCMAKE_CXX_FLAGS_RELEASE=${EXTERNALS_MSVC_RUNTIME_LIBRARY}
            -DCMAKE_MSVC_RUNTIME_LIBRARY=${CMAKE_MSVC_RUNTIME_LIBRARY}
    )
endif()

# ---------------------------------------------------------------------------------
# CLEAN EXTERNALS TARGET (Cleans only the external projects)
# ---------------------------------------------------------------------------------

add_custom_target(clean_externals
        COMMAND ${CMAKE_COMMAND} -E remove_directory ${EXTERNALS_BINARY_DIR}
        COMMENT "Cleaning external projects."
)

# ---------------------------------------------------------------------------------
# ZLIB CPP
# ---------------------------------------------------------------------------------

add_external_git_project(
        NAME                zlib_cpp
        GIT_REPOSITORY      https://github.com/madler/zlib.git
        GIT_TAG             ${EXTERNAL_ZLIB_VERSION}
        GIT_SHALLOW         OFF
        CMAKE_ARGS          ${EXTERNAL_COMMON_CMAKE_ARGS}
        -DZLIB_BUILD_SHARED=OFF
        -DZLIB_BUILD_STATIC=ON
        -DZLIB_BUILD_TESTING=OFF
        EXTERNALS_BIN_DIR   ${EXTERNALS_BINARY_DIR}
        BUILD_TYPE          Release
)

# Include a subdirectory of ANTLR4 which is an irregular subfolder.
include_directories(${EXTERNALS_BINARY_DIR}/miniaudio_c/src)

# ---------------------------------------------------------------------------------
# OPENSSL CPP
# ---------------------------------------------------------------------------------

add_external_git_project_with_make(
        NAME                openssl_cpp
        URL                 https://github.com/openssl/openssl/archive/refs/tags/openssl-${EXTERNAL_OPENSSL_VERSION}.zip
        CONFIG_ARGS         no-shared no-module no-ssl2 no-ssl3 no-idea no-psk no-srp no-ec2m no-weak-ssl-ciphers no-legacy
        EXTERNALS_BIN_DIR   ${EXTERNALS_BINARY_DIR}
)

# ---------------------------------------------------------------------------------
# HTTPLIB CPP
# ---------------------------------------------------------------------------------

add_external_git_project(
        NAME                httplib_cpp
        GIT_REPOSITORY      https://github.com/yhirose/cpp-httplib.git
        GIT_TAG             ${EXTERNAL_HTTPLIB_VERSION}
        CMAKE_ARGS          ${EXTERNAL_COMMON_CMAKE_ARGS}
        -DBUILD_SHARED_LIBS=OFF
        -DHTTPLIB_USE_CERTS_FROM_MACOSX_KEYCHAIN=ON
        EXTERNALS_BIN_DIR   ${EXTERNALS_BINARY_DIR}
        BUILD_TYPE          Release
)

# ---------------------------------------------------------------------------------
# IXWEBSOCKET CPP
# ---------------------------------------------------------------------------------

add_external_git_project(
        NAME                ixwebsocket_cpp
        GIT_REPOSITORY      https://github.com/machinezone/IXWebSocket.git
        GIT_TAG             ${EXTERNAL_IXWEBSOCKET_VERSION}
        CMAKE_ARGS          ${EXTERNAL_COMMON_CMAKE_ARGS}
        -DBUILD_SHARED_LIBS=OFF
        -DUSE_TLS=ON
        -DUSE_WS=ON
        EXTERNALS_BIN_DIR   ${EXTERNALS_BINARY_DIR}
        BUILD_TYPE          Release
)

# ---------------------------------------------------------------------------------
# SPDLOG CPP
# ---------------------------------------------------------------------------------

add_external_git_project(
        NAME                spdlog_cpp
        GIT_REPOSITORY      https://github.com/gabime/spdlog.git
        GIT_TAG             ${EXTERNAL_SPDLOG_VERSION}
        GIT_SHALLOW         OFF
        CMAKE_ARGS          ${EXTERNAL_COMMON_CMAKE_ARGS}
        EXTERNALS_BIN_DIR   ${EXTERNALS_BINARY_DIR}
        BUILD_TYPE          Release
)

# ---------------------------------------------------------------------------------
# JSON CPP
# ---------------------------------------------------------------------------------

add_external_git_project(
        NAME                json_cpp
        GIT_REPOSITORY      https://github.com/nlohmann/json.git
        GIT_TAG             ${EXTERNAL_JSON_VERSION}
        GIT_SHALLOW         OFF
        CMAKE_ARGS          ${EXTERNAL_COMMON_CMAKE_ARGS}
        EXTERNALS_BIN_DIR   ${EXTERNALS_BINARY_DIR}
        BUILD_TYPE          Release
)
