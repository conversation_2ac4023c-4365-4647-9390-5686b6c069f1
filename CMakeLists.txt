#
#  Copyright © 2025 Present, Arkin Terli. All rights reserved.
#
#  NOTICE:  All information contained herein is, and remains the property of Arkin Terli.
#  The intellectual and technical concepts contained herein are proprietary to Arkin Terli
#  and may be covered by U.S. and Foreign Patents, patents in process, and are protected by
#  trade secret or copyright law. Dissemination of this information or reproduction of this
#  material is strictly forbidden unless prior written permission is obtained from Arkin Terli.

cmake_minimum_required(VERSION 3.24)

project(ProxtCpp)

# Set compiler configurations.

set(CMAKE_CONFIGURATION_TYPES "Debug;Release" CACHE STRING "" FORCE)
set(CMAKE_POSITION_INDEPENDENT_CODE ON)
set(CMAKE_BUILD_WITH_INSTALL_RPATH ON)

# Set RPATH to look in the loader directory first to load libraries.
if(APPLE)
    set(CMAKE_INSTALL_RPATH "@loader_path")
elseif (UNIX)
    set(CMAKE_INSTALL_RPATH "$ORIGIN")
endif()

# Set default build type as Release
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Set compiler flags
set(CMAKE_CXX_STANDARD 20)

if(MSVC)
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Zi")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2")

    # Use MultiThreaded (/MT) by default if not set.
    if ("${CMAKE_MSVC_RUNTIME_LIBRARY}" STREQUAL "")
        set(CMAKE_MSVC_RUNTIME_LIBRARY MultiThreaded)
    endif()
else()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3")
endif()

# Set external lib versions.
set(EXTERNAL_ZLIB_VERSION 5a82f71)
set(EXTERNAL_OPENSSL_VERSION 3.5.0)
set(EXTERNAL_HTTPLIB_VERSION v0.22.0)
set(EXTERNAL_IXWEBSOCKET_VERSION v11.4.6)
set(EXTERNAL_SPDLOG_VERSION v1.15.3)

include(Externals/Externals.cmake)

# Include ProxyLib headers.
include_directories(Targets/ProxyLib)

# Add targets to build and install.
add_subdirectory(Targets/ProxyLib)
add_subdirectory(Targets/ProxyTest)
